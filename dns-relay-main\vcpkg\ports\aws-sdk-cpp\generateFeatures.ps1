[CmdletBinding()]
param(
    [Parameter(Mandatory=$false)][string]$PortDirectory = $PSScriptRoot,
    [Parameter(Mandatory=$false)][string]$vcpkg = "$PSScriptRoot/../../vcpkg"
)

$ErrorActionPreference = "Stop"

$ManifestIn = "$PortDirectory/vcpkg.in.json"
$ManifestOut = "$PortDirectory/vcpkg.json"

$manifest = Get-Content $ManifestIn | ConvertFrom-Json
$version = $manifest.version

Write-Host "Using version from vcpkg.in.json: $version"

$ExtractedSources = "${env:TEMP}/aws-sdk-cpp-generateFeatures-$version"
if (-not (Test-Path $ExtractedSources)) {
    if (Test-Path "$ExtractedSources.tmp") {
        Remove-Item -Force "$ExtractedSources.tmp"
    }
    git clone --depth=1 "https://github.com/aws/aws-sdk-cpp" "$ExtractedSources.tmp" | Out-Host
    git -c "$ExtractedSources.tmp" checkout $version
    Move-Item "$ExtractedSources.tmp" "$ExtractedSources"
}
Write-Host "Using sources directory: $ExtractedSources"

$subfolders = Get-ChildItem -Path "$ExtractedSources\generated\src\aws-cpp-sdk-*", "$ExtractedSources\src\aws-cpp-sdk*" | Sort-Object -Property Name

$manifest | Add-Member `
    -NotePropertyName '$note' `
    -NotePropertyValue 'Automatically generated by generateFeatures.ps1 from vcpkg.in.json, do not edit manually'
$manifest | Add-Member -NotePropertyName 'features' -NotePropertyValue @{}

function GetDescription($dir, $modulename)
{
    if (Test-Path "$dir\CMakeLists.txt")
    {
        $descs = @(Select-String -Path "$dir\CMakeLists.txt" -Pattern "`"C\+\+ SDK for the AWS [^`"]*`"")
        if ($descs.count -eq 1) {
            $desc = $descs[0].Matches.Value -replace "`"",""
            "$desc"
        }
        else { "C++ SDK for the AWS $modulename service" }
    }
    else { "C++ SDK for the AWS $modulename service" }
}

$featureDependencies = @{}
Select-String -Path "$ExtractedSources\cmake\sdksCommon.cmake" -Pattern "list\(APPEND SDK_DEPENDENCY_LIST `"([\w-]+):([\w-,]+)`"\)" -AllMatches `
| ForEach-Object { $_.Matches } `
| ForEach-Object { $featureDependencies[$_.Groups[1].Value] = @($_.Groups[2].Value -split "," `
| Where-Object { $_ -ne "core" }) }

foreach ($subfolder in $subfolders)
{
    $modulename = $subfolder.name -replace "^aws-cpp-sdk-",""
    if ($modulename -match "-tests`$") { continue }
    if ($modulename -match "-sample`$") { continue }
    if ($modulename -eq "core") { continue }

    $lowermodulename = $modulename.ToLower()

    $featureObj = @{ description = (GetDescription $subfolder $modulename) }

    if ($featureDependencies.ContainsKey($lowermodulename)) {
        $featureObj.dependencies = ,@{ name = "aws-sdk-cpp"; "default-features" = $false; "features" = $featureDependencies[$lowermodulename] }
    }

    $manifest.features.Add("$lowermodulename", $featureObj)
}

[IO.File]::WriteAllText($ManifestOut, (ConvertTo-Json -Depth 10 -InputObject $manifest))

& $vcpkg format-manifest --feature-flags=-manifests $ManifestOut
