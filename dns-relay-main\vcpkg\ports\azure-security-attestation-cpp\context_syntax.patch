diff --git a/sdk/attestation/azure-security-attestation/inc/azure/attestation/attestation_client.hpp b/sdk/attestation/azure-security-attestation/inc/azure/attestation/attestation_client.hpp
index d7c44d494..31fcb8d3f 100644
--- a/sdk/attestation/azure-security-attestation/inc/azure/attestation/attestation_client.hpp
+++ b/sdk/attestation/azure-security-attestation/inc/azure/attestation/attestation_client.hpp
@@ -176,7 +176,7 @@ namespace Azure { namespace Security { namespace Attestation {
      * specified service instance.
      */
     Response<Models::OpenIdMetadata> GetOpenIdMetadata(
-        Azure::Core::Context const& context = Azure::Core::Context::ApplicationContext) const;
+        Azure::Core::Context const& context = {}) const;
 
     /**
      * @brief Retrieve the attestation signing certificates for this attestation instance.
