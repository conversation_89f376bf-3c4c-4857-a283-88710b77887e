diff --git a/CMakeLists.txt b/CMakeLists.txt
index d348072..d5b6be9 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -172,7 +172,7 @@ if (NOT ASMTK_EMBED)
     $<$<NOT:$<CONFIG:Debug>>:${ASMTK_PRIVATE_CFLAGS_REL}>)
 
   if(ASMJIT_EXTERNAL)
-    target_link_libraries(asmtk PUBLIC ${ASMJIT_LIBRARY})
+    target_link_libraries(asmtk PUBLIC asmjit::asmjit)
     find_path(ASMJIT_INCLUDE_DIR NAMES asmjit/asmjit.h)
     target_include_directories(asmtk PRIVATE ${ASMJIT_INCLUDE_DIR})
   else()
