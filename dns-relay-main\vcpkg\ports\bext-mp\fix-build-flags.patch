diff --git a/CMakeLists.txt b/CMakeLists.txt
index deaa67c..fb82294 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -34,10 +34,10 @@ target_sources(mp
 if(PROJECT_IS_TOP_LEVEL)
   if("${CMAKE_CXX_COMPILER_ID}" MATCHES "Clang")
     target_compile_options(mp INTERFACE
-      $<BUILD_INTERFACE:-stdlib=libc++ -Wall -Wextra -Wpedantic -Werror>)
+      $<BUILD_INTERFACE:-stdlib=libc++ -Wall -Wextra -Wpedantic>)
     target_link_options(mp INTERFACE $<BUILD_INTERFACE:-stdlib=libc++>)
   elseif("${CMAKE_CXX_COMPILER_ID}" STREQUAL "GNU")
-    target_compile_options(mp INTERFACE $<BUILD_INTERFACE:-Wall -Wextra -Wpedantic -Werror>)
+    target_compile_options(mp INTERFACE $<BUILD_INTERFACE:-Wall -Wextra -Wpedantic>)
   endif()
 endif()
 
