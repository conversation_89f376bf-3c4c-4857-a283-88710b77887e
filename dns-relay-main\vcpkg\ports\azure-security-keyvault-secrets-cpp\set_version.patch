diff --git a/sdk/keyvault/azure-security-keyvault-secrets/CMakeLists.txt b/sdk/keyvault/azure-security-keyvault-secrets/CMakeLists.txt
index e5acb6001..647285994 100644
--- a/sdk/keyvault/azure-security-keyvault-secrets/CMakeLists.txt
+++ b/sdk/keyvault/azure-security-keyvault-secrets/CMakeLists.txt
@@ -133,6 +133,7 @@ target_compile_definitions(azure-security-keyvault-secrets PRIVATE _azure_BUILDI
 create_code_coverage(keyvault azure-security-keyvault-secrets azure-security-keyvault-secrets-test "tests?/*;samples?/*")
 
 get_az_version("${CMAKE_CURRENT_SOURCE_DIR}/src/private/package_version.hpp")
+set_target_properties(azure-security-keyvault-secrets PROPERTIES VERSION ${AZ_LIBRARY_VERSION})
 generate_documentation(azure-security-keyvault-secrets ${AZ_LIBRARY_VERSION})
 if(BUILD_TESTING)
 
