diff --git a/3rdparty/lexy/include/lexy/_detail/swar.hpp b/3rdparty/lexy/include/lexy/_detail/swar.hpp
index d7734d0..7250912 100644
--- a/3rdparty/lexy/include/lexy/_detail/swar.hpp
+++ b/3rdparty/lexy/include/lexy/_detail/swar.hpp
@@ -141,8 +141,13 @@ constexpr std::size_t swar_find_difference(swar_int lhs, swar_int rhs)
     auto bit_idx = __builtin_ctzll(mask);
 #elif defined(_MSC_VER)
     unsigned long bit_idx;
-    if (!_BitScanForward64(&bit_idx, mask))
-        bit_idx         = 64;
+    #if defined(_WIN64)
+        if (!_BitScanForward64(&bit_idx, mask))
+            bit_idx         = 64;
+    #elif defined(_WIN32)
+        if (!_BitScanForward(&bit_idx, mask))
+            bit_idx         = 32;
+    #endif
 #else
 #    error "unsupported compiler; please file an issue"
 #endif
diff --git a/CMakeLists.txt b/CMakeLists.txt
index 6d463ba..90abc0e 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -173,6 +173,10 @@ else()
     add_library(${BTCPP_LIBRARY} STATIC ${BT_SOURCE})
 endif()
 
+if (MSVC)
+    target_compile_definitions(${BTCPP_LIBRARY} PRIVATE NOMINMAX)
+endif ()
+
 message(STATUS "BTCPP_EXTRA_LIBRARIES: ${BTCPP_EXTRA_LIBRARIES}")
 
 target_link_libraries(${BTCPP_LIBRARY}
