diff --git a/include/amqpcpp/message.h b/include/amqpcpp/message.h
index 0a35838..96b43e1 100644
--- a/include/amqpcpp/message.h
+++ b/include/amqpcpp/message.h
@@ -80,7 +80,7 @@ protected:
         // safety-check: on 32-bit platforms size_t is obviously also a 32-bit dword
         // in which case casting the uint64_t to a size_t could result in truncation
         // here we check whether the given size fits inside a size_t
-        if (std::numeric_limits<size_t>::max() < size) throw std::runtime_error("message is too big for this system");
+        if ((std::numeric_limits<size_t>::max)() < size) throw std::runtime_error("message is too big for this system");
 
         // store the new size
         _bodySize = size;
@@ -98,7 +98,7 @@ protected:
         if (_mutableBody)
         {
             // prevent overflow
-            size = std::min(size, _bodySize - _filled);
+            size = (std::min)(size, _bodySize - _filled);
             
             // append more data
             memcpy(_mutableBody + _filled, buffer, (size_t)size);
@@ -121,7 +121,7 @@ protected:
             _body = _mutableBody;
             
             // store the initial data
-            _filled = std::min((size_t)size, (size_t)_bodySize);
+            _filled = (std::min)((size_t)size, (size_t)_bodySize);
             memcpy(_mutableBody, buffer, _filled);
         }
             
