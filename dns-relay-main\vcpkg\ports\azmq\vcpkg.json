{"name": "azmq", "version-date": "2023-03-23", "description": ["Boost Asio style bindings for ZeroMQ", "This library is built on top of ZeroMQ's standard C interface and is intended to work well with C++ applications which use the Boost libraries in general, and Asio in particular.", "The main abstraction exposed by the library is azmq::socket which provides an Asio style socket interface to the underlying zeromq socket and interfaces with Asio's io_service(). The socket implementation participates in the io_service's reactor for asynchronous IO and may be freely mixed with other Asio socket types (raw TCP/UDP/Serial/etc.)."], "homepage": "https://github.com/zeromq/azmq", "dependencies": ["boost-asio", "boost-assert", "boost-config", "boost-container", "boost-format", "boost-intrusive", "boost-iterator", "boost-lexical-cast", "boost-logic", "boost-optional", "boost-random", "boost-range", "boost-regex", "boost-system", "boost-thread", "boost-utility", "zeromq"]}