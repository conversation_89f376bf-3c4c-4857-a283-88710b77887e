{"$comment": ["NOTE: All changes made to this file will get overwritten by the next port release.", "Please contribute your changes to https://github.com/Azure/azure-sdk-for-cpp."], "name": "azure-storage-queues-cpp", "version-semver": "12.4.0", "port-version": 1, "description": ["Microsoft Azure Storage Queues SDK for C++", "This library provides Azure Storage Queues SDK."], "homepage": "https://github.com/Azure/azure-sdk-for-cpp/tree/main/sdk/storage/azure-storage-queues", "license": "MIT", "dependencies": [{"name": "azure-storage-common-cpp", "default-features": false, "version>=": "12.8.0"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}