diff --git a/CMakeLists.txt b/CMakeLists.txt
index d4c6762..c7b15b6 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -197,7 +197,7 @@ function (add_mp_library name)
   target_compile_definitions(${name}
     PUBLIC ${add_mp_library_COMPILE_DEFINITIONS})
   target_include_directories(${name}
-    PUBLIC ${add_mp_library_INCLUDE_DIRECTORIES})
+    PUBLIC $<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/${add_mp_library_INCLUDE_DIRECTORIES}> $<INSTALL_INTERFACE:include>)
   if (add_mp_library_DEPENDS)
     add_dependencies(${name} ${add_mp_library_DEPENDS})
   endif ()
@@ -363,6 +363,7 @@ if(BUILD_TESTING)
 endif()
 
 install(DIRECTORY include/mp DESTINATION include)
-install(TARGETS mp DESTINATION lib RUNTIME DESTINATION bin)
+install(TARGETS mp EXPORT unofficial-mp-targets DESTINATION lib RUNTIME DESTINATION bin)
 install(FILES LICENSE.rst DESTINATION share/mp)
+include(0007-unofficial-export.cmake)
 install(TARGETS gen-expr-info RUNTIME DESTINATION bin)
\ No newline at end of file
