diff --git a/provisioning_client/CMakeLists.txt b/provisioning_client/CMakeLists.txt
index 39f269d51..ff53ca32d 100644
--- a/provisioning_client/CMakeLists.txt
+++ b/provisioning_client/CMakeLists.txt
@@ -357,7 +357,7 @@ if(${use_installed_dependencies})
         set(CMAKE_INSTALL_LIBDIR "lib")
     endif()
 
-    install(TARGETS ${provisioning_libs} EXPORT azure_prov_sdksTargets
+    install(TARGETS ${provisioning_libs} EXPORT azure_iot_sdksTargets
         LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
         ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
         RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
@@ -372,26 +372,6 @@ if(${use_installed_dependencies})
         VERSION ${PROV_SDK_VERSION}
         COMPATIBILITY SameMajorVersion
     )
-
-    configure_file("../configs/${PROJECT_NAME}Config.cmake"
-        "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}/${PROJECT_NAME}Config.cmake"
-        COPYONLY
-    )
-
-    install(EXPORT azure_prov_sdksTargets
-        FILE
-            "${PROJECT_NAME}Targets.cmake"
-        DESTINATION
-            ${package_location}
-    )
-
-    install(
-        FILES
-            "../configs/${PROJECT_NAME}Config.cmake"
-            "${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}/${PROJECT_NAME}ConfigVersion.cmake"
-        DESTINATION
-            ${package_location}
-    )
 else()
     # Install Provisioning libs
     if(NOT DEFINED CMAKE_INSTALL_LIBDIR)
