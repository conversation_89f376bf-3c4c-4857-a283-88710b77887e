{"name": "asio", "version": "1.32.0", "description": "Asio is a cross-platform C++ library for network and low-level I/O programming that provides developers with a consistent asynchronous model using a modern C++ approach.", "homepage": "https://think-async.com/Asio/", "documentation": "https://think-async.com/Asio/Documentation.html", "license": "BSL-1.0", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"coroutine": {"description": "Boost.Coroutine (optional) if you use spawn() to launch coroutines", "dependencies": ["boost-coroutine"]}, "openssl": {"description": "OpenSSL (optional) if you use Asio's SSL support.", "dependencies": ["openssl"]}, "regex": {"description": "Boost.Regex (optional) if you use any of the read_until() or async_read_until() overloads that take a boost::regex parameter.", "dependencies": ["boost-regex"]}}}