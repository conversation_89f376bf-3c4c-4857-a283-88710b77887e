diff --git a/configs/azure_iot_build_rules.cmake b/configs/azure_iot_build_rules.cmake
index 2f7e4ae..00c94c7 100644
--- a/configs/azure_iot_build_rules.cmake
+++ b/configs/azure_iot_build_rules.cmake
@@ -73,7 +73,7 @@ if(MSVC)
 elseif(UNIX) #LINUX OR APPLE
     if(NOT (IN_OPENWRT OR APPLE))
         # _XOPEN_SOURCE=500 is required for glibc to expose random and srandom.
-        set (CMAKE_C_FLAGS "-D_POSIX_C_SOURCE=200112L -D_XOPEN_SOURCE=500 ${CMAKE_C_FLAGS}")
+        set (CMAKE_C_FLAGS "-D_POSIX_C_SOURCE=200809L -D_XOPEN_SOURCE=500 ${CMAKE_C_FLAGS}")
     endif()
 endif()
 
