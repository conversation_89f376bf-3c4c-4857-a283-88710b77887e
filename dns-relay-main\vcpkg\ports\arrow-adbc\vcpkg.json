{"name": "arrow-adbc", "version": "16", "description": "Apache Arrow ADBC: Database Connectivity API for Arrow-based data systems", "homepage": "https://arrow.apache.org/adbc/", "license": "Apache-2.0", "dependencies": [{"name": "arrow", "default-features": false, "features": ["dataset", "parquet"]}, "fmt", "nanoarrow", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"bigquery": {"description": "Enable bigquery support", "supports": "!windows"}, "flightsql": {"description": "Enable flightsql support", "supports": "!windows"}, "postgresql": {"description": "Enable PostgreSQL support", "dependencies": ["libpq"]}, "snowflake": {"description": "Enable snowflake support", "supports": "!windows"}, "sqlite": {"description": "Enable SQLite support", "dependencies": ["sqlite3"]}}}