{"name": "adios2", "version": "2.9.2", "description": "Next generation of ADIOS developed in the Exascale Computing Program", "homepage": "https://github.com/ornladios/ADIOS2", "license": "Apache-2.0", "dependencies": ["blosc", "bzip2", {"name": "hdf5", "default-features": false}, "libffi", "libpng", "libsodium", "pugixml", {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}, "zeromq"], "features": {"cuda": {"description": "Enable CUDA support", "dependencies": ["cuda"]}, "mpi": {"description": "Enable MPI support", "dependencies": [{"name": "hdf5", "default-features": false, "features": ["parallel"]}, "mpi"]}, "python": {"description": "Enable Python bindings", "dependencies": ["python3", {"name": "python3", "host": true}]}, "zfp": {"description": "Enable zfp support", "dependencies": ["zfp"]}}}