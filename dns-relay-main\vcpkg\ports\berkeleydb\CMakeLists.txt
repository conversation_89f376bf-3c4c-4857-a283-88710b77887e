cmake_minimum_required(VERSION 3.8)
project(berkeleydb C CXX)

option(INSTALL_HEADERS "Install header files" ON)

add_definitions(
	-DWIN32
	-D_WINDOWS
	-D_CRT_SECURE_NO_DEPRECATE
	-D_CRT_NONSTDC_NO_DEPRECATE
	-D_LIB
	-DUNICODE
	-D_UNICODE
)

if (BUILD_SHARED_LIBS)
	add_definitions(
		-DDB_CREATE_DLL
		-D_USRDLL
	)
else()
	add_definitions(
		-D_LIB
	)
endif()

add_library(libdb48 
	btree/bt_compact.c
	btree/bt_compare.c
	btree/bt_compress.c
	btree/bt_conv.c
	btree/bt_curadj.c
	btree/bt_cursor.c
	btree/bt_delete.c
	btree/bt_method.c
	btree/bt_open.c
	btree/bt_put.c
	btree/bt_rec.c
	btree/bt_reclaim.c
	btree/bt_recno.c
	btree/bt_rsearch.c
	btree/bt_search.c
	btree/bt_split.c
	btree/bt_stat.c
	btree/bt_upgrade.c
	btree/bt_verify.c
	btree/btree_auto.c
	db/crdel_auto.c
	db/crdel_rec.c
	common/crypto_stub.c
	cxx/cxx_db.cpp
	cxx/cxx_dbc.cpp
	cxx/cxx_dbt.cpp
	cxx/cxx_env.cpp
	cxx/cxx_except.cpp
	cxx/cxx_lock.cpp
	cxx/cxx_logc.cpp
	cxx/cxx_mpool.cpp
	cxx/cxx_multi.cpp
	cxx/cxx_seq.cpp
	cxx/cxx_txn.cpp
	db/db.c
	db/db_am.c
	db/db_auto.c
	common/db_byteorder.c
	db/db_cam.c
	db/db_cds.c
	db/db_conv.c
	db/db_dispatch.c
	db/db_dup.c
	common/db_err.c
	common/db_getlong.c
	common/db_idspace.c
	common/db_compint.c
	db/db_iface.c
	db/db_join.c
	common/db_log2.c
	db/db_meta.c
	db/db_method.c
	db/db_open.c
	db/db_overflow.c
	db/db_ovfl_vrfy.c
	db/db_pr.c
	db/db_rec.c
	db/db_reclaim.c
	db/db_remove.c
	db/db_rename.c
	db/db_ret.c
	db/db_setid.c
	db/db_setlsn.c
	common/db_shash.c
	db/db_sort_multiple.c
	db/db_stati.c
	db/db_truncate.c
	db/db_upg.c
	db/db_upg_opd.c
	db/db_vrfy.c
	db/db_vrfyutil.c
	db/partition.c
	dbm/dbm.c
	dbreg/dbreg.c
	dbreg/dbreg_auto.c
	dbreg/dbreg_rec.c
	dbreg/dbreg_stat.c
	dbreg/dbreg_util.c
	common/dbt.c
	env/env_alloc.c
	env/env_config.c
	env/env_failchk.c
	env/env_file.c
	env/env_globals.c
	env/env_method.c
	env/env_name.c
	env/env_open.c
	env/env_recover.c
	env/env_region.c
	env/env_register.c
	env/env_sig.c
	env/env_stat.c
	fileops/fileops_auto.c
	fileops/fop_basic.c
	fileops/fop_rec.c
	fileops/fop_util.c
	hash/hash.c
	hash/hash_auto.c
	hash/hash_conv.c
	hash/hash_dup.c
	hash/hash_func.c
	hash/hash_meta.c
	hash/hash_method.c
	hash/hash_open.c
	hash/hash_page.c
	hash/hash_rec.c
	hash/hash_reclaim.c
	hash/hash_stat.c
	hash/hash_upgrade.c
	hash/hash_verify.c
	hmac/hmac.c
	hsearch/hsearch.c
	build_windows/libdb.def
	lock/lock.c
	lock/lock_deadlock.c
	lock/lock_failchk.c
	lock/lock_id.c
	lock/lock_list.c
	lock/lock_method.c
	lock/lock_region.c
	lock/lock_stat.c
	lock/lock_timer.c
	lock/lock_util.c
	log/log.c
	log/log_archive.c
	log/log_compare.c
	log/log_debug.c
	log/log_get.c
	log/log_method.c
	log/log_put.c
	log/log_stat.c
	common/mkpath.c
	mp/mp_alloc.c
	mp/mp_bh.c
	mp/mp_fget.c
	mp/mp_fmethod.c
	mp/mp_fopen.c
	mp/mp_fput.c
	mp/mp_fset.c
	mp/mp_method.c
	mp/mp_mvcc.c
	mp/mp_region.c
	mp/mp_register.c
	mp/mp_resize.c
	mp/mp_stat.c
	mp/mp_sync.c
	mp/mp_trickle.c
	mutex/mut_alloc.c
	mutex/mut_failchk.c
	mutex/mut_method.c
	mutex/mut_region.c
	mutex/mut_stat.c
	mutex/mut_win32.c
	common/openflags.c
	os/os_abort.c
	os/os_addrinfo.c
	os_windows/os_abs.c
	os/os_alloc.c
	os_windows/os_clock.c
	os_windows/os_config.c
	os_windows/os_cpu.c
	os/os_ctime.c
	os_windows/os_dir.c
	os_windows/os_errno.c
	os_windows/os_fid.c
	os_windows/os_flock.c
	os_windows/os_fsync.c
	os_windows/os_getenv.c
	os_windows/os_handle.c
	os_windows/os_map.c
	os_windows/os_mkdir.c
	os_windows/os_open.c
	os/os_pid.c
	os_windows/os_rename.c
	os/os_root.c
	os/os_rpath.c
	os_windows/os_rw.c
	os_windows/os_seek.c
	os/os_stack.c
	os_windows/os_stat.c
	os/os_tmpdir.c
	os_windows/os_truncate.c
	os/os_uid.c
	os_windows/os_unlink.c
	os_windows/os_yield.c
	qam/qam.c
	qam/qam_auto.c
	qam/qam_conv.c
	qam/qam_files.c
	qam/qam_method.c
	qam/qam_open.c
	qam/qam_rec.c
	qam/qam_stat.c
	qam/qam_upgrade.c
	qam/qam_verify.c
	rep/rep_auto.c
	rep/rep_backup.c
	rep/rep_elect.c
	rep/rep_lease.c
	rep/rep_log.c
	rep/rep_method.c
	rep/rep_record.c
	rep/rep_region.c
	rep/rep_stat.c
	rep/rep_util.c
	rep/rep_verify.c
	repmgr/repmgr_auto.c
	repmgr/repmgr_elect.c
	repmgr/repmgr_method.c
	repmgr/repmgr_msg.c
	repmgr/repmgr_net.c
	repmgr/repmgr_queue.c
	repmgr/repmgr_sel.c
	repmgr/repmgr_stat.c
	repmgr/repmgr_util.c
	repmgr/repmgr_windows.c
	sequence/seq_stat.c
	sequence/sequence.c
	hmac/sha1.c
	clib/strsep.c
	txn/txn.c
	txn/txn_auto.c
	txn/txn_chkpt.c
	txn/txn_failchk.c
	txn/txn_method.c
	txn/txn_rec.c
	txn/txn_recover.c
	txn/txn_region.c
	txn/txn_stat.c
	txn/txn_util.c
	common/os_method.c
	common/util_cache.c
	common/util_log.c
	common/util_sig.c
	common/zerofill.c
)

include_directories(${CMAKE_CURRENT_SOURCE_DIR} build_windows dbinc)

target_link_libraries(libdb48 PUBLIC ws2_32.lib)

install(TARGETS libdb48
    RUNTIME DESTINATION bin
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
)

if(INSTALL_HEADERS)
	file(GLOB HEADERS build_windows/*.h)
	install(FILES ${HEADERS} DESTINATION include)
endif()