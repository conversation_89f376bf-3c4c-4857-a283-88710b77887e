# DNS中继服务器 - 快速入门指南

## 🚀 5分钟快速开始

### 第一步：确认编译完成
```bash
cd dns-relay-main/build
ls -la dns_relay  # Linux/WSL
dir dns_relay.exe # Windows
```

### 第二步：准备配置文件
确保build目录下有dnsrelay.txt文件：
```bash
# 检查配置文件
cat dnsrelay.txt | head -5
```

### 第三步：启动服务器
```bash
# 推荐：使用端口8053和公共DNS
./dns_relay -p 8053 -s ******* -d

# 或使用国内DNS（阿里DNS）
./dns_relay -p 8053 -s ********* -d

# 如果需要使用标准DNS端口53（需要管理员权限）
sudo ./dns_relay -d
```

### 第四步：测试功能
```bash
# 方法1：使用Python测试脚本（推荐）
cd dns-relay-main
python test_dns.py

# 方法2：使用nslookup（可能有兼容性问题）
nslookup
> server 127.0.0.1
> set port=8053
> baidu.com
> 2qq.cn
> bupt
> exit
```

## 📋 常用命令速查

### 启动命令
```bash
# 基本启动（推荐配置）
./dns_relay -p 8053 -s ******* -d

# 国内用户推荐
./dns_relay -p 8053 -s ********* -d

# 详细调试
./dns_relay -p 8053 -s ******* -v

# 大缓存模式
./dns_relay -p 8053 -s ******* -c 4096

# 自定义超时时间
./dns_relay -p 8053 -s ******* -t 3000
```

### 测试命令
```bash
# Python测试脚本（推荐）
python test_dns.py

# 使用dig测试
dig @127.0.0.1 -p 8053 <域名>

# nslookup交互模式
nslookup
> server 127.0.0.1
> set port=8053
> <域名>

# 测试IPv6
dig @127.0.0.1 -p 8053 <域名> AAAA
```

## 🔧 故障排除

### 问题1：端口被占用
```
错误：bind: Address already in use
解决：./dns_relay -p 5353
```

### 问题2：找不到配置文件
```
错误：fopen: No such file or directory
解决：确保dnsrelay.txt在当前目录，或使用 -f 指定路径
```

### 问题3：权限不足
```
错误：Permission denied
解决：使用sudo或选择非特权端口
```

### 问题4：上游DNS无法连接
```
错误：日志显示"Not found"，正常域名无法解析
原因：默认上游DNS(********)是校园网内部DNS
解决：更换为公共DNS服务器
./dns_relay -p 8053 -s ******* -d     # Google DNS
./dns_relay -p 8053 -s ********* -d   # 阿里DNS
```

### 问题5：nslookup无法连接
```
错误：No response from server
原因：Windows nslookup对自定义端口支持有限
解决：使用Python测试脚本
python test_dns.py
```

## 📊 功能验证清单

- [ ] 服务器成功启动，显示监听端口
- [ ] 正常域名解析（如baidu.com）
- [ ] 黑名单域名拦截（如test0）
- [ ] 缓存功能工作（第二次查询更快）
- [ ] 自定义IP映射（如bupt解析到**************）
- [ ] 日志输出正常

## 🎯 性能测试

### 简单性能测试
```bash
# 测试缓存效果
time nslookup google.com 127.0.0.1 -port=5353  # 第一次
time nslookup google.com 127.0.0.1 -port=5353  # 第二次（应该更快）
```

### 并发测试
```bash
# 同时发起多个查询
for i in {1..10}; do
    nslookup google.com 127.0.0.1 -port=5353 &
done
wait
```

## 📝 配置文件示例

### 基本配置 (dnsrelay.txt)
```
# 黑名单域名
0.0.0.0 ads.example.com
0.0.0.0 malware.com

# 自定义解析
************* local.server
******* my.dns

# 常用网站
************* sina.com
************** sohu.com
```

## 🔍 日志分析

### 正常日志示例
```
INFO DNS Server initialized on port 5353
INFO Cache hit: baidu.com          # 缓存命中
INFO Blacklisted: test0            # 黑名单拦截
DEBUG Cache miss: google.com       # 缓存未命中
INFO Cache insert: google.com      # 添加到缓存
```

### 异常日志
```
ERROR Failed to create UDP server  # 端口占用
ERROR Failed to load blacklist     # 配置文件问题
ERROR Failed to pack DNS response  # 协议错误
```

## 🛠️ 高级用法

### 1. 作为系统DNS服务器
```bash
# 修改系统DNS设置指向127.0.0.1:5353
# Linux: 编辑 /etc/resolv.conf
# Windows: 网络设置中修改DNS服务器
```

### 2. 网络环境测试
```bash
# 校园网环境
./dns_relay -p 5353 -s ********

# 公网环境
./dns_relay -p 5353 -s *******

# 国内环境
./dns_relay -p 5353 -s *********
```

### 3. 性能调优
```bash
# 大缓存 + 长超时
./dns_relay -p 5353 -c 8192 -t 10000

# 快速响应模式
./dns_relay -p 5353 -c 1024 -t 2000
```

## 📞 获取帮助

### 查看帮助信息
```bash
./dns_relay -h
```

### 查看详细配置
```bash
./dns_relay -v  # 启动时会显示所有配置参数
```

### 常见问题
1. **Q**: 如何停止服务器？
   **A**: 按Ctrl+C优雅停止

2. **Q**: 如何查看缓存状态？
   **A**: 使用-v参数启动，会显示缓存操作

3. **Q**: 支持哪些DNS记录类型？
   **A**: 主要支持A记录(IPv4)和AAAA记录(IPv6)

4. **Q**: 如何提高性能？
   **A**: 增加缓存大小(-c参数)，选择快速的上游DNS

## 🎉 成功标志

当您看到以下输出时，说明DNS中继服务器已成功运行：

```
2024-06-02 10:30:15.123 INFO DNS Server initialized on port 5353
2024-06-02 10:30:15.124 INFO DNS Server starting...
```

现在您可以开始使用DNS中继服务器了！
