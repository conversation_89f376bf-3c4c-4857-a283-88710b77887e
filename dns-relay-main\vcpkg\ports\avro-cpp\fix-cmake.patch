diff --git a/lang/c++/CMakeLists.txt b/lang/c++/CMakeLists.txt
index 19059a4..c49e9c2 100644
--- a/lang/c++/CMakeLists.txt
+++ b/lang/c++/CMakeLists.txt
@@ -58,17 +58,12 @@ set(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} ${CMAKE_SOURCE_DIR})
 if (WIN32 AND NOT CYGWIN AND NOT MSYS)
     add_definitions (/EHa)
     add_definitions (
-        -DNOMINMAX
-        -DBOOST_REGEX_DYN_LINK
-        -DBOOST_FILESYSTEM_DYN_LINK
-        -DBOOST_SYSTEM_DYN_LINK
-        -DBOOST_IOSTREAMS_DYN_LINK
-        -DBOOST_PROGRAM_OPTIONS_DYN_LINK
-        -DBOOST_ALL_NO_LIB)
+        -DNOMINMAX)
 endif()
 
 if (CMAKE_COMPILER_IS_GNUCXX)
-    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wduplicated-cond -Wduplicated-branches -Wlogical-op -Wuseless-cast -Wconversion -pedantic -Werror")
+    # Remove " -Werror" because of warning from boost-math (will require C++ 14 soon)
+    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wduplicated-cond -Wduplicated-branches -Wlogical-op -Wuseless-cast -Wconversion -pedantic")
 if (AVRO_ADD_PROTECTOR_FLAGS)
     set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -fstack-protector-all -D_GLIBCXX_DEBUG")
     # Unset _GLIBCXX_DEBUG for avrogencpp.cc because using Boost Program Options
@@ -82,16 +77,7 @@ endif ()
 find_package (Boost 1.38 REQUIRED
     COMPONENTS filesystem iostreams program_options regex system)
 
-include(FetchContent)
-FetchContent_Declare(
-        fmt
-        GIT_REPOSITORY  https://github.com/fmtlib/fmt.git
-        GIT_TAG         10.2.1
-        GIT_PROGRESS    TRUE
-        USES_TERMINAL_DOWNLOAD TRUE
-)
-FetchContent_MakeAvailable(fmt)
-
+find_package(fmt CONFIG REQUIRED)
 find_package(Snappy)
 if (SNAPPY_FOUND)
     set(SNAPPY_PKG libsnappy)
@@ -128,7 +114,7 @@ set (AVRO_SOURCE_FILES
         impl/CustomAttributes.cc
         )
 
-add_library (avrocpp SHARED ${AVRO_SOURCE_FILES})
+add_library (avrocpp ${AVRO_SOURCE_FILES})
 
 set_property (TARGET avrocpp
     APPEND PROPERTY COMPILE_DEFINITIONS AVRO_DYN_LINK)
@@ -194,6 +180,7 @@ target_include_directories(avrocpp PUBLIC
   $<INSTALL_INTERFACE:include>
 )
 
+if(BUILD_TESTING)
 enable_testing()
 
 macro (unittest name)
@@ -220,23 +207,26 @@ unittest (CommonsSchemasTests)
 add_dependencies (AvrogencppTestReservedWords cpp_reserved_words_hh)
 
 add_dependencies (AvrogencppTests bigrecord_hh bigrecord_r_hh bigrecord2_hh
-    tweet_hh
-    union_array_union_hh union_map_union_hh union_conflict_hh
-    recursive_hh reuse_hh circulardep_hh tree1_hh tree2_hh crossref_hh
-    primitivetypes_hh empty_record_hh cpp_reserved_words_union_typedef_hh
-    union_empty_record_hh)
-
+   tweet_hh
+   union_array_union_hh union_map_union_hh union_conflict_hh
+   recursive_hh reuse_hh circulardep_hh tree1_hh tree2_hh crossref_hh
+   primitivetypes_hh empty_record_hh cpp_reserved_words_union_typedef_hh
+   union_empty_record_hh)
+endif()
 include (InstallRequiredSystemLibraries)
 
 set (CPACK_PACKAGE_FILE_NAME "avrocpp-${AVRO_VERSION_MAJOR}")
 
 include (CPack)
 
-install (TARGETS avrocpp avrocpp_s
-    LIBRARY DESTINATION lib
-    ARCHIVE DESTINATION lib
-    RUNTIME DESTINATION lib)
+install(TARGETS avrocpp EXPORT unofficial-avro-cpp)
 
+install(
+    EXPORT unofficial-avro-cpp
+    FILE unofficial-avro-cpp-config.cmake
+    DESTINATION share/unofficial-avro-cpp
+    NAMESPACE unofficial::avro-cpp::
+)
 install (TARGETS avrogencpp RUNTIME DESTINATION bin)
 
 install (DIRECTORY include/avro DESTINATION include
