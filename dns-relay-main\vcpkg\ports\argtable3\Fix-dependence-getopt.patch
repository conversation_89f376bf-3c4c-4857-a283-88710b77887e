diff --git a/src/CMakeLists.txt b/src/CMakeLists.txt
index 8ea5334..1ec6bbe 100644
--- a/src/CMakeLists.txt
+++ b/src/CMakeLists.txt
@@ -86,6 +86,11 @@ target_include_directories(argtable3 PUBLIC
   "$<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>"
 )
 
+if(NOT ARGTABLE3_REPLACE_GETOPT)
+  find_package(unofficial-getopt-win32 REQUIRED)
+  target_link_libraries(argtable3 PRIVATE unofficial::getopt-win32::getopt)
+endif()
+
 set_target_properties(argtable3 PROPERTIES
   VERSION "${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}.${PROJECT_VERSION_PATCH}"
   SOVERSION "${PROJECT_VERSION_MAJOR}"
