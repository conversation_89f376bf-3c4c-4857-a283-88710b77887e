{"$comment": ["NOTE: All changes made to this file will get overwritten by the next port release.", "Please contribute your changes to https://github.com/Azure/azure-sdk-for-cpp."], "name": "azure-storage-files-shares-cpp", "version-semver": "12.13.0", "description": ["Microsoft Azure Storage Files Shares SDK for C++", "This library provides Azure Storage Files Shares SDK."], "homepage": "https://github.com/Azure/azure-sdk-for-cpp/tree/main/sdk/storage/azure-storage-files-shares", "license": "MIT", "dependencies": [{"name": "azure-storage-common-cpp", "default-features": false, "version>=": "12.10.0"}, {"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}]}