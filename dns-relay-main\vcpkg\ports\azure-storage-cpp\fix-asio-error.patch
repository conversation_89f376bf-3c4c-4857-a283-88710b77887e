diff --git a/Microsoft.WindowsAzure.Storage/includes/wascore/util.h b/Microsoft.WindowsAzure.Storage/includes/wascore/util.h
index fcd86cd..5f02120 100644
--- a/Microsoft.WindowsAzure.Storage/includes/wascore/util.h
+++ b/Microsoft.WindowsAzure.Storage/includes/wascore/util.h
@@ -125,7 +125,7 @@ namespace azure { namespace storage { namespace core {
         WASTORAGE_API static std::shared_ptr<web::http::client::http_client> get_http_client(const web::uri& uri, const web::http::client::http_client_config& config);
 
     private:
-        static const boost::asio::io_service& s_service;
+        static const boost::asio::io_context& s_service;
         WASTORAGE_API static std::map<utility::string_t, std::shared_ptr<web::http::client::http_client>> s_http_clients;
         WASTORAGE_API static std::mutex s_mutex;
     };
diff --git a/Microsoft.WindowsAzure.Storage/src/cloud_common.cpp b/Microsoft.WindowsAzure.Storage/src/cloud_common.cpp
index 7dfcfeb..0749252 100644
--- a/Microsoft.WindowsAzure.Storage/src/cloud_common.cpp
+++ b/Microsoft.WindowsAzure.Storage/src/cloud_common.cpp
@@ -71,7 +71,7 @@ namespace azure { namespace storage {
         throw std::invalid_argument(protocol::error_invalid_ip_address);
 #else
         boost::system::error_code error;
-        auto addr = boost::asio::ip::address::from_string(address, error);
+        auto addr = boost::asio::ip::make_address(address, error);
         if (error.value() == 0)
         {
             if (addr.is_v4())
diff --git a/Microsoft.WindowsAzure.Storage/src/timer_handler.cpp b/Microsoft.WindowsAzure.Storage/src/timer_handler.cpp
index f33d183..fc5e50d 100644
--- a/Microsoft.WindowsAzure.Storage/src/timer_handler.cpp
+++ b/Microsoft.WindowsAzure.Storage/src/timer_handler.cpp
@@ -86,7 +86,7 @@ namespace azure {    namespace storage {    namespace core {
     pplx::task<void> timer_handler::timeout_after(const std::chrono::milliseconds& time)
     {
         m_timer = std::make_shared<boost::asio::basic_waitable_timer<std::chrono::steady_clock>>(crossplat::threadpool::shared_instance().service());
-        m_timer->expires_from_now(std::chrono::duration_cast<std::chrono::steady_clock::duration>(time));
+        m_timer->expires_after(std::chrono::duration_cast<std::chrono::steady_clock::duration>(time));
         std::weak_ptr<timer_handler> weak_this_pointer = shared_from_this();
         auto callback = [weak_this_pointer](const boost::system::error_code& ec)
         {
diff --git a/Microsoft.WindowsAzure.Storage/src/util.cpp b/Microsoft.WindowsAzure.Storage/src/util.cpp
index cf0a311..8f321de 100644
--- a/Microsoft.WindowsAzure.Storage/src/util.cpp
+++ b/Microsoft.WindowsAzure.Storage/src/util.cpp
@@ -440,7 +440,7 @@ namespace azure { namespace storage {  namespace core {
     }
 
 #ifndef _WIN32
-    const boost::asio::io_service& http_client_reusable::s_service = crossplat::threadpool::shared_instance().service();
+    const boost::asio::io_context& http_client_reusable::s_service = crossplat::threadpool::shared_instance().service();
     std::map<utility::string_t, std::shared_ptr<web::http::client::http_client>> http_client_reusable::s_http_clients;
     std::mutex http_client_reusable::s_mutex;
 
