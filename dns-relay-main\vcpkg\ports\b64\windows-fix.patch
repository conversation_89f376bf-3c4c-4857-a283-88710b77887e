diff --git a/include/b64/ccommon.h b/include/b64/ccommon.h
index 2b614df..0e46141 100644
--- a/include/b64/ccommon.h
+++ b/include/b64/ccommon.h
@@ -10,11 +10,12 @@ For details, see http://sourceforge.net/projects/libb64
 
 #define BASE64_VER_MAJOR	2
 #define BASE64_VER_MINOR	0
+#include "b64_config.h"
 
 #ifndef HAVE_SIZE_T
   #ifdef _WIN32
     #include <crtdefs.h>
-  #elseif defined (__unix__) || (defined (__APPLE__) && defined (__MACH__))
+  #elif defined (__unix__) || (defined (__APPLE__) && defined (__MACH__))
     #include <stdlib.h>
   #else
     typedef unsigned long size_t;
diff --git a/include/b64/cdecode.h b/include/b64/cdecode.h
index d6ff24c..4553efc 100644
--- a/include/b64/cdecode.h
+++ b/include/b64/cdecode.h
@@ -24,11 +24,11 @@ typedef struct
 	char plainchar;
 } base64_decodestate;
 
-extern void base64_init_decodestate(base64_decodestate* state_in);
+extern LIBB64 void base64_init_decodestate(base64_decodestate* state_in);
 
-extern size_t base64_decode_maxlength(size_t encode_len);
+extern LIBB64 size_t base64_decode_maxlength(size_t encode_len);
 
-extern int base64_decode_value(signed char value_in);
-extern size_t base64_decode_block(const char* code_in, const size_t length_in, void* plaintext_out, base64_decodestate* state_in);
+extern LIBB64 int base64_decode_value(signed char value_in);
+extern LIBB64 size_t base64_decode_block(const char* code_in, const size_t length_in, void* plaintext_out, base64_decodestate* state_in);
 
 #endif /* BASE64_CDECODE_H */
diff --git a/include/b64/cencode.h b/include/b64/cencode.h
index 96b0cdb..1feb695 100644
--- a/include/b64/cencode.h
+++ b/include/b64/cencode.h
@@ -31,12 +31,12 @@ typedef struct
 	char result;
 } base64_encodestate;
 
-extern void base64_init_encodestate(base64_encodestate* state_in);
+extern LIBB64 void base64_init_encodestate(base64_encodestate* state_in);
 
-extern size_t base64_encode_length(size_t plain_len, base64_encodestate* state_in);
+extern LIBB64 size_t base64_encode_length(size_t plain_len, base64_encodestate* state_in);
 
-extern char base64_encode_value(signed char value_in);
-extern size_t base64_encode_block(const void* plaintext_in, const size_t length_in, char* code_out, base64_encodestate* state_in);
-extern size_t base64_encode_blockend(char* code_out, base64_encodestate* state_in);
+extern LIBB64 char base64_encode_value(signed char value_in);
+extern LIBB64 size_t base64_encode_block(const void* plaintext_in, const size_t length_in, char* code_out, base64_encodestate* state_in);
+extern LIBB64 size_t base64_encode_blockend(char* code_out, base64_encodestate* state_in);
 
 #endif /* BASE64_CENCODE_H */
diff --git a/include/b64/decode.h b/include/b64/decode.h
index b2362e5..dd772d4 100644
--- a/include/b64/decode.h
+++ b/include/b64/decode.h
@@ -22,23 +22,23 @@ namespace base64
 		base64_decodestate _state;
 		int _buffersize;
 
-		decoder(int buffersize_in = BUFFERSIZE)
+		LIBB64 decoder(int buffersize_in = BUFFERSIZE)
 		: _buffersize(buffersize_in)
 		{
 			base64_init_decodestate(&_state);
 		}
 
-		int decode(char value_in)
+		LIBB64 int decode(char value_in)
 		{
 			return base64_decode_value(value_in);
 		}
 
-		std::streamsize decode(const char* code_in, const std::streamsize length_in, char* plaintext_out)
+		LIBB64 std::streamsize decode(const char* code_in, const std::streamsize length_in, char* plaintext_out)
 		{
 			return base64_decode_block(code_in, static_cast<int>(length_in), plaintext_out, &_state);
 		}
 
-		void decode(std::istream& istream_in, std::ostream& ostream_in)
+		LIBB64 void decode(std::istream& istream_in, std::ostream& ostream_in)
 		{
 			base64_init_decodestate(&_state);
 			//
diff --git a/include/b64/encode.h b/include/b64/encode.h
index c1a5f88..ff2c9b4 100644
--- a/include/b64/encode.h
+++ b/include/b64/encode.h
@@ -22,28 +22,28 @@ namespace base64
 		base64_encodestate _state;
 		int _buffersize;
 
-		encoder(int buffersize_in = BUFFERSIZE)
+		LIBB64 encoder(int buffersize_in = BUFFERSIZE)
 			: _buffersize(buffersize_in)
 		{
 			base64_init_encodestate(&_state);
 		}
 
-		int encode(char value_in)
+		LIBB64 int encode(char value_in)
 		{
 			return base64_encode_value(value_in);
 		}
 
-		std::streamsize encode(const char* code_in, const std::streamsize length_in, char* plaintext_out)
+		LIBB64 std::streamsize encode(const char* code_in, const std::streamsize length_in, char* plaintext_out)
 		{
 			return base64_encode_block(code_in, static_cast<int>(length_in), plaintext_out, &_state);
 		}
 
-		int encode_end(char* plaintext_out)
+		LIBB64 int encode_end(char* plaintext_out)
 		{
 			return base64_encode_blockend(plaintext_out, &_state);
 		}
 
-		void encode(std::istream& istream_in, std::ostream& ostream_in)
+		LIBB64 void encode(std::istream& istream_in, std::ostream& ostream_in)
 		{
 			base64_init_encodestate(&_state);
 			//
