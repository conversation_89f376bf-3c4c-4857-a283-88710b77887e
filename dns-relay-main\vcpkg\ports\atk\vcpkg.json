{"name": "atk", "version": "2.38.0", "port-version": 10, "description": "GNOME Accessibility Toolkit", "homepage": "https://developer.gnome.org/atk/", "license": "LGPL-2.0-or-later", "supports": "!xbox", "dependencies": [{"name": "gettext", "host": true, "default-features": false, "features": ["tools"]}, "gettext-libintl", "glib", {"name": "glib", "host": true}, {"name": "vcpkg-tool-meson", "host": true}], "features": {"introspection": {"description": "Build with introspection", "supports": "!static", "dependencies": ["gobject-introspection"]}}}