{"name": "abseil", "version": "20250127.1", "port-version": 1, "description": ["Abseil is an open-source collection of C++ library code designed to augment the C++ standard library. The Abseil library code is collected from Google's own C++ code base, has been extensively tested and used in production, and is the same code we depend on in our daily coding lives.", "In some cases, Abseil provides pieces missing from the C++ standard; in others, Abseil provides alternatives to the standard for special needs we've found through usage in the Google code base. We denote those cases clearly within the library code we provide you.", "Abseil is not meant to be a competitor to the standard library; we've just found that many of these utilities serve a purpose within our code base, and we now want to provide those resources to the C++ community as a whole."], "homepage": "https://github.com/abseil/abseil-cpp", "license": "Apache-2.0", "dependencies": [{"name": "vcpkg-cmake", "host": true}, {"name": "vcpkg-cmake-config", "host": true}], "features": {"cxx17": {"description": "Enable compiler C++17."}, "test-helpers": {"description": "Build Abseil's test helpers", "dependencies": [{"name": "abseil", "features": ["cxx17"]}, {"name": "gtest", "features": ["cxx17"]}]}}}