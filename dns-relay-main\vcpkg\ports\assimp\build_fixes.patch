diff --git a/CMakeLists.txt b/CMakeLists.txt
index d45eb22..0b5da28 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -307,7 +307,6 @@ ELSEIF(MSVC)
   ENDIF()
   # supress warning for double to float conversion if Double precision is activated
   ADD_COMPILE_OPTIONS(/wd4244)
-  SET(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /D_DEBUG /Zi /Od")
   # Allow user to disable PDBs
   if(ASSIMP_INSTALL_PDB)
     SET(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /Zi")
@@ -315,8 +314,7 @@ ELSEIF(MSVC)
   elseif((GENERATOR_IS_MULTI_CONFIG) OR (CMAKE_BUILD_TYPE MATCHES Release))
     message("-- MSVC PDB generation disabled. Release binary will not be debuggable.")
   endif()
-  # Source code is encoded in UTF-8
-  ADD_COMPILE_OPTIONS(/source-charset:utf-8)
+
 ELSEIF (CMAKE_CXX_COMPILER_ID MATCHES "Clang" )
   IF(NOT ASSIMP_HUNTER_ENABLED)
     SET(CMAKE_POSITION_INDEPENDENT_CODE ON)
@@ -444,7 +442,7 @@ ENDIF()
 
 set(GENERATED_DIR "${CMAKE_CURRENT_BINARY_DIR}/generated")
 
-IF(ASSIMP_HUNTER_ENABLED)
+IF(0)
   set(CONFIG_INSTALL_DIR "lib/cmake/${PROJECT_NAME}")
   set(CMAKE_CONFIG_TEMPLATE_FILE "cmake-modules/assimp-hunter-config.cmake.in")
   set(NAMESPACE "${PROJECT_NAME}::")
@@ -452,7 +450,7 @@ IF(ASSIMP_HUNTER_ENABLED)
   set(VERSION_CONFIG "${GENERATED_DIR}/${PROJECT_NAME}ConfigVersion.cmake")
   set(PROJECT_CONFIG "${GENERATED_DIR}/${PROJECT_NAME}Config.cmake")
 ELSE()
-  set(CONFIG_INSTALL_DIR "${ASSIMP_LIB_INSTALL_DIR}/cmake/assimp-${ASSIMP_VERSION_MAJOR}.${ASSIMP_VERSION_MINOR}")
+  set(CONFIG_INSTALL_DIR "${ASSIMP_LIB_INSTALL_DIR}/cmake/assimp")
   set(CMAKE_CONFIG_TEMPLATE_FILE "cmake-modules/assimp-plain-config.cmake.in")
   string(TOLOWER ${PROJECT_NAME} PROJECT_NAME_LOWERCASE)
   set(NAMESPACE "${PROJECT_NAME_LOWERCASE}::")
@@ -496,14 +494,13 @@ ENDIF()
 
 # Search for external dependencies, and build them from source if not found
 # Search for zlib
-IF(ASSIMP_HUNTER_ENABLED)
-  hunter_add_package(ZLIB)
-  find_package(ZLIB CONFIG REQUIRED)
+IF(1)
+  find_package(ZLIB REQUIRED)
 
   add_definitions(-DASSIMP_BUILD_NO_OWN_ZLIB)
   set(ZLIB_FOUND TRUE)
-  set(ZLIB_LIBRARIES ZLIB::zlib)
-  set(ASSIMP_BUILD_MINIZIP TRUE)
+  set(ZLIB_LIBRARIES ZLIB::ZLIB)
+  set(ASSIMP_BUILD_MINIZIP OFF)
 ELSE()
   # If the zlib is already found outside, add an export in case assimpTargets can't find it.
   IF( ZLIB_FOUND AND ASSIMP_INSTALL)
@@ -547,13 +544,13 @@ ELSE()
   INCLUDE_DIRECTORIES(${ZLIB_INCLUDE_DIR})
 ENDIF()
 
-IF( NOT IOS )
+IF( 0 )
   IF( NOT ASSIMP_BUILD_MINIZIP )
     use_pkgconfig(UNZIP minizip)
   ENDIF()
 ELSE ()
   IF( NOT BUILD_SHARED_LIBS )
-    IF( NOT ASSIMP_BUILD_MINIZIP )
+    IF( 0 )
       use_pkgconfig(UNZIP minizip)
     ENDIF()
   ENDIF ()
diff --git a/cmake-modules/assimp-plain-config.cmake.in b/cmake-modules/assimp-plain-config.cmake.in
index 6551dcb..3064f70 100644
--- a/cmake-modules/assimp-plain-config.cmake.in
+++ b/cmake-modules/assimp-plain-config.cmake.in
@@ -1,5 +1,19 @@
 @PACKAGE_INIT@
 
+include(CMakeFindDependencyMacro)
+
+if(NOT @BUILD_SHARED_LIBS@)
+    find_dependency(kubazip CONFIG)
+    find_dependency(unofficial-minizip CONFIG)
+    find_dependency(pugixml CONFIG)
+    find_dependency(poly2tri CONFIG)
+    find_dependency(polyclipping CONFIG)
+    find_dependency(RapidJSON CONFIG)
+    find_dependency(Stb MODULE)
+    find_dependency(utf8cpp CONFIG)
+    find_dependency(ZLIB)
+endif()
+
 include("${CMAKE_CURRENT_LIST_DIR}/@TARGETS_EXPORT_NAME@.cmake")
 
 set(ASSIMP_ROOT_DIR ${PACKAGE_PREFIX_DIR})
diff --git a/code/AssetLib/3MF/D3MFExporter.cpp b/code/AssetLib/3MF/D3MFExporter.cpp
index 6c09f09..7b3410a 100644
--- a/code/AssetLib/3MF/D3MFExporter.cpp
+++ b/code/AssetLib/3MF/D3MFExporter.cpp
@@ -57,7 +57,7 @@ OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 #ifdef ASSIMP_USE_HUNTER
 #include <zip/zip.h>
 #else
-#include <contrib/zip/src/zip.h>
+#include <zip/zip.h>
 #endif
 
 namespace Assimp {
diff --git a/code/AssetLib/Blender/BlenderTessellator.h b/code/AssetLib/Blender/BlenderTessellator.h
index e43535f..a0104c1 100644
--- a/code/AssetLib/Blender/BlenderTessellator.h
+++ b/code/AssetLib/Blender/BlenderTessellator.h
@@ -143,7 +143,7 @@ namespace Assimp
 
 #if ASSIMP_BLEND_WITH_POLY_2_TRI
 
-#include "contrib/poly2tri/poly2tri/poly2tri.h"
+#include <poly2tri/poly2tri.h>
 
 namespace Assimp
 {
diff --git a/code/AssetLib/IFC/IFCGeometry.cpp b/code/AssetLib/IFC/IFCGeometry.cpp
index d488b23..1a6c0c7 100644
--- a/code/AssetLib/IFC/IFCGeometry.cpp
+++ b/code/AssetLib/IFC/IFCGeometry.cpp
@@ -45,8 +45,8 @@ OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 #include "IFCUtil.h"
 #include "Common/PolyTools.h"
 #include "PostProcessing/ProcessHelper.h"
-#include "contrib/poly2tri/poly2tri/poly2tri.h"
-#include "contrib/clipper/clipper.hpp"
+#include <poly2tri/poly2tri.h>
+#include <polyclipping/clipper.hpp>
 
 #include <iterator>
 #include <memory>
diff --git a/code/AssetLib/IFC/IFCLoader.cpp b/code/AssetLib/IFC/IFCLoader.cpp
index 13ea2d4..aeeb311 100644
--- a/code/AssetLib/IFC/IFCLoader.cpp
+++ b/code/AssetLib/IFC/IFCLoader.cpp
@@ -53,7 +53,7 @@ OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 #ifdef ASSIMP_USE_HUNTER
 #include <minizip/unzip.h>
 #else
-#include <unzip.h>
+#include <minizip/unzip.h>
 #endif
 #endif
 
diff --git a/code/AssetLib/IFC/IFCOpenings.cpp b/code/AssetLib/IFC/IFCOpenings.cpp
index 1d37dd8..eadbc86 100644
--- a/code/AssetLib/IFC/IFCOpenings.cpp
+++ b/code/AssetLib/IFC/IFCOpenings.cpp
@@ -47,8 +47,8 @@ OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 #include "IFCUtil.h"
 #include "Common/PolyTools.h"
 #include "PostProcessing/ProcessHelper.h"
-#include "contrib/poly2tri/poly2tri/poly2tri.h"
-#include "contrib/clipper/clipper.hpp"
+#include <poly2tri/poly2tri.h>
+#include <polyclipping/clipper.hpp>
 
 #include <deque>
 #include <forward_list>
diff --git a/code/AssetLib/MMD/MMDPmxParser.cpp b/code/AssetLib/MMD/MMDPmxParser.cpp
index 5a3e61d..e444dc8 100644
--- a/code/AssetLib/MMD/MMDPmxParser.cpp
+++ b/code/AssetLib/MMD/MMDPmxParser.cpp
@@ -42,7 +42,7 @@ OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 #include <utility>
 #include "MMDPmxParser.h"
 #include <assimp/StringUtils.h>
-#include "utf8.h"
+#include <utf8cpp/utf8.h>
 #include <assimp/Exceptional.h>
 
 namespace pmx
diff --git a/code/AssetLib/SIB/SIBImporter.cpp b/code/AssetLib/SIB/SIBImporter.cpp
index e55e675..fb1a12b 100644
--- a/code/AssetLib/SIB/SIBImporter.cpp
+++ b/code/AssetLib/SIB/SIBImporter.cpp
@@ -56,7 +56,7 @@ OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 #include <assimp/ByteSwapper.h>
 #include <assimp/StreamReader.h>
 #include <assimp/TinyFormatter.h>
-#include "utf8.h"
+#include <utf8cpp/utf8.h>
 #include <assimp/importerdesc.h>
 #include <assimp/scene.h>
 #include <assimp/DefaultLogger.hpp>
diff --git a/code/AssetLib/STEPParser/STEPFileEncoding.cpp b/code/AssetLib/STEPParser/STEPFileEncoding.cpp
index d7f512c..94275f1 100644
--- a/code/AssetLib/STEPParser/STEPFileEncoding.cpp
+++ b/code/AssetLib/STEPParser/STEPFileEncoding.cpp
@@ -45,7 +45,7 @@ OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  */
 #include "STEPFileEncoding.h"
 #include <assimp/fast_atof.h>
-#include "utf8.h"
+#include <utf8cpp/utf8.h>
 
 #include <memory>
 
diff --git a/code/CMakeLists.txt b/code/CMakeLists.txt
index 9b27086..b7c2ccb 100644
--- a/code/CMakeLists.txt
+++ b/code/CMakeLists.txt
@@ -1050,8 +1050,8 @@ ELSE() # IF (ASSIMP_BUILD_USD_IMPORTER)
 ENDIF() # IF (ASSIMP_BUILD_USD_IMPORTER)
 
 # pugixml
-IF(ASSIMP_HUNTER_ENABLED)
-  hunter_add_package(pugixml)
+IF(1)
+  #hunter_add_package(pugixml)
   find_package(pugixml CONFIG REQUIRED)
 ELSE()
   SET( Pugixml_SRCS
@@ -1063,30 +1063,30 @@ ELSE()
 ENDIF()
 
 # utf8
-IF(ASSIMP_HUNTER_ENABLED)
-  hunter_add_package(utf8)
+IF(1)
+  #hunter_add_package(utf8)
   find_package(utf8cpp CONFIG REQUIRED)
 ELSE()
   INCLUDE_DIRECTORIES("../contrib/utf8cpp/source")
 ENDIF()
 
 # polyclipping
-#IF(ASSIMP_HUNTER_ENABLED)
+IF(1)
 #  hunter_add_package(polyclipping)
-#  find_package(polyclipping CONFIG REQUIRED)
-#ELSE()
+  find_package(polyclipping CONFIG REQUIRED)
+ELSE()
   SET( Clipper_SRCS
     ../contrib/clipper/clipper.hpp
     ../contrib/clipper/clipper.cpp
   )
   SOURCE_GROUP( Contrib\\Clipper FILES ${Clipper_SRCS})
-#ENDIF()
+ENDIF()
 
 # poly2tri
-#IF(ASSIMP_HUNTER_ENABLED)
+IF(1)
 #  hunter_add_package(poly2tri)
-#  find_package(poly2tri CONFIG REQUIRED)
-#ELSE()
+  find_package(poly2tri CONFIG REQUIRED)
+ELSE()
   SET( Poly2Tri_SRCS
     ../contrib/poly2tri/poly2tri/common/shapes.cc
     ../contrib/poly2tri/poly2tri/common/shapes.h
@@ -1101,12 +1101,12 @@ ENDIF()
     ../contrib/poly2tri/poly2tri/sweep/sweep_context.h
   )
   SOURCE_GROUP( Contrib\\Poly2Tri FILES ${Poly2Tri_SRCS})
-#ENDIF()
+ENDIF()
 
 # minizip/unzip
-IF(ASSIMP_HUNTER_ENABLED)
-  hunter_add_package(minizip)
-  find_package(minizip CONFIG REQUIRED)
+IF(1)
+  #hunter_add_package(minizip)
+  find_package(unofficial-minizip CONFIG REQUIRED)
 ELSE()
   SET( unzip_SRCS
     ../contrib/unzip/crypt.h
@@ -1121,9 +1121,9 @@ ENDIF()
 # zip (https://github.com/kuba--/zip)
 separate_arguments(ASSIMP_EXPORTERS_LIST UNIX_COMMAND ${ASSIMP_EXPORTERS_ENABLED})
 IF(3MF IN_LIST ASSIMP_EXPORTERS_LIST)
-  IF(ASSIMP_HUNTER_ENABLED)
-    hunter_add_package(zip)
-    find_package(zip CONFIG REQUIRED)
+  IF(1)
+    #hunter_add_package(zip)
+    find_package(kubazip CONFIG REQUIRED)
   ELSE()
     SET( ziplib_SRCS
       ../contrib/zip/src/miniz.h
@@ -1143,7 +1143,7 @@ IF(3MF IN_LIST ASSIMP_EXPORTERS_LIST)
 ENDIF()
 
 # openddlparser
-IF(ASSIMP_HUNTER_ENABLED)
+IF(0)
   hunter_add_package(openddlparser)
   find_package(openddlparser CONFIG REQUIRED)
 ELSE()
@@ -1166,7 +1166,7 @@ ELSE()
 ENDIF()
 
 # Open3DGC
-IF(ASSIMP_HUNTER_ENABLED)
+IF(0)
   # Nothing to do, not available in Hunter yet.
 ELSE()
   SET ( open3dgc_SRCS
@@ -1201,6 +1201,7 @@ ELSE()
     ../contrib/Open3DGC/o3dgcVector.inl
   )
   SOURCE_GROUP( Contrib\\open3dgc FILES ${open3dgc_SRCS})
+  INCLUDE_DIRECTORIES("../contrib")
 ENDIF()
 
 # Check dependencies for glTF importer with Open3DGC-compression.
@@ -1209,7 +1210,7 @@ ENDIF()
 IF (NOT WIN32)
   FIND_PACKAGE(RT QUIET)
 ENDIF ()
-IF (NOT ASSIMP_HUNTER_ENABLED AND (RT_FOUND OR WIN32))
+IF (1)
   SET( ASSIMP_IMPORTER_GLTF_USE_OPEN3DGC 1 )
   ADD_DEFINITIONS( -DASSIMP_IMPORTER_GLTF_USE_OPEN3DGC=1 )
 ELSE ()
@@ -1219,9 +1220,10 @@ ELSE ()
 ENDIF ()
 
 # RapidJSON
-IF(ASSIMP_HUNTER_ENABLED)
-  hunter_add_package(RapidJSON)
+IF(1)
+  #hunter_add_package(RapidJSON)
   find_package(RapidJSON CONFIG REQUIRED)
+  ADD_DEFINITIONS( -DRAPIDJSON_HAS_STDSTRING=1)
 ELSE()
   INCLUDE_DIRECTORIES("../contrib/rapidjson/include")
   ADD_DEFINITIONS( -DRAPIDJSON_HAS_STDSTRING=1)
@@ -1232,9 +1234,8 @@ ELSE()
 ENDIF()
 
 # stb
-IF(ASSIMP_HUNTER_ENABLED)
-  hunter_add_package(stb)
-  find_package(stb CONFIG REQUIRED)
+IF(1)
+  find_package(Stb REQUIRED)
 ELSE()
   SET( stb_SRCS
     ../contrib/stb/stb_image.h
@@ -1256,7 +1257,7 @@ IF( MSVC OR "${CMAKE_CXX_SIMULATE_ID}" MATCHES "MSVC") # clang with MSVC ABI
   ADD_DEFINITIONS( -D_CRT_SECURE_NO_WARNINGS )
 endif ()
 
-IF(NOT ASSIMP_HUNTER_ENABLED)
+IF(0)
   if (UNZIP_FOUND)
     SET (unzip_compile_SRCS "")
   else ()
@@ -1310,7 +1311,7 @@ SET( assimp_src
 )
 ADD_DEFINITIONS( -DOPENDDLPARSER_BUILD )
 
-IF(NOT ASSIMP_HUNTER_ENABLED)
+IF(1)
   INCLUDE_DIRECTORIES(
       ${IRRXML_INCLUDE_DIR}
       ../contrib/openddlparser/include
@@ -1412,45 +1413,47 @@ TARGET_INCLUDE_DIRECTORIES ( assimp PUBLIC
   $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../include>
   $<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}/../include>
   $<INSTALL_INTERFACE:${ASSIMP_INCLUDE_INSTALL_DIR}>
+  PRIVATE
+    ${Stb_INCLUDE_DIR}
 )
 
-IF(ASSIMP_HUNTER_ENABLED)
+IF(1)
   TARGET_LINK_LIBRARIES(assimp
-      PUBLIC
-      #polyclipping::polyclipping
-      openddlparser::openddl_parser
-      #poly2tri::poly2tri
-      minizip::minizip
-      ZLIB::zlib
-      RapidJSON::rapidjson
-      utf8cpp
+      PRIVATE
+      polyclipping::polyclipping
+      #openddlparser::openddl_parser
+      ${OPENDDL_PARSER_LIBRARIES}
+      poly2tri::poly2tri
+      unofficial::minizip::minizip
+      ZLIB::ZLIB
+      rapidjson
+      utf8cpp::utf8cpp
       pugixml
-      stb::stb
   )
-  if(TARGET zip::zip)
-    target_link_libraries(assimp PUBLIC zip::zip)
+  if(1)
+    target_link_libraries(assimp PRIVATE kubazip::kubazip)
   endif()
 
   if (ASSIMP_BUILD_DRACO)
     target_link_libraries(assimp PRIVATE ${draco_LIBRARIES})
   endif()
 ELSE()
-  TARGET_LINK_LIBRARIES(assimp ${ZLIB_LIBRARIES} ${OPENDDL_PARSER_LIBRARIES})
+  TARGET_LINK_LIBRARIES(assimp PRIVATE ${ZLIB_LIBRARIES} ${OPENDDL_PARSER_LIBRARIES})
   if (ASSIMP_BUILD_DRACO)
-    target_link_libraries(assimp ${draco_LIBRARIES})
+    target_link_libraries(assimp PRIVATE ${draco_LIBRARIES})
   endif()
 ENDIF()
 
 if(ASSIMP_ANDROID_JNIIOSYSTEM)
   set(ASSIMP_ANDROID_JNIIOSYSTEM_PATH port/AndroidJNI)
   add_subdirectory(../${ASSIMP_ANDROID_JNIIOSYSTEM_PATH}/ ../${ASSIMP_ANDROID_JNIIOSYSTEM_PATH}/)
-  target_link_libraries(assimp android_jniiosystem)
+  target_link_libraries(assimp PRIVATE android_jniiosystem)
 endif()
 
 IF (ASSIMP_BUILD_NONFREE_C4D_IMPORTER)
-  TARGET_LINK_LIBRARIES(assimp optimized ${C4D_RELEASE_LIBRARIES})
-  TARGET_LINK_LIBRARIES(assimp debug ${C4D_DEBUG_LIBRARIES})
-  TARGET_LINK_LIBRARIES(assimp ${C4D_EXTRA_LIBRARIES})
+  TARGET_LINK_LIBRARIES(assimp PRIVATE optimized ${C4D_RELEASE_LIBRARIES})
+  TARGET_LINK_LIBRARIES(assimp PRIVATE debug ${C4D_DEBUG_LIBRARIES})
+  TARGET_LINK_LIBRARIES(assimp PRIVATE ${C4D_EXTRA_LIBRARIES})
 ENDIF ()
 
 if( MSVC )
@@ -1491,13 +1494,13 @@ if (MINGW)
     ARCHIVE_OUTPUT_NAME assimp
   )
   if (NOT BUILD_SHARED_LIBS)
-    TARGET_LINK_LIBRARIES ( assimp -static-libgcc -static-libstdc++ -Wl,-Bstatic -lstdc++ -lwinpthread ) # winpthread is for libminizip.
+    TARGET_LINK_LIBRARIES ( assimp PRIVATE -static-libgcc -static-libstdc++ -Wl,-Bstatic -lstdc++ -lwinpthread ) # winpthread is for libminizip.
   endif ()
 endif()
 
 if (${CMAKE_SYSTEM_NAME} MATCHES "WindowsStore")
     target_compile_definitions(assimp PUBLIC WindowsStore)
-    TARGET_LINK_LIBRARIES(assimp advapi32)
+    TARGET_LINK_LIBRARIES(assimp PRIVATE advapi32)
 endif()
 
 SET_TARGET_PROPERTIES( assimp PROPERTIES
@@ -1527,14 +1530,14 @@ ENDIF()
 
 # Build against external unzip, or add ../contrib/unzip so
 # assimp can #include "unzip.h"
-IF(NOT ASSIMP_HUNTER_ENABLED)
+IF(0)
   if (UNZIP_FOUND)
     INCLUDE_DIRECTORIES(${UNZIP_INCLUDE_DIRS})
     # TODO if cmake required version has been updated to >3.12.0, collapse this to the second case only
     if(${CMAKE_VERSION} VERSION_LESS "3.12.0")
-      TARGET_LINK_LIBRARIES(assimp ${UNZIP_LIBRARIES})
+      TARGET_LINK_LIBRARIES(assimp PRIVATE ${UNZIP_LIBRARIES})
     else()
-      TARGET_LINK_LIBRARIES(assimp ${UNZIP_LINK_LIBRARIES})
+      TARGET_LINK_LIBRARIES(assimp PRIVATE ${UNZIP_LINK_LIBRARIES})
     endif()
   else ()
     INCLUDE_DIRECTORIES("../")
@@ -1543,7 +1546,7 @@ ENDIF()
 
 # Add RT-extension library for glTF importer with Open3DGC-compression.
 IF (RT_FOUND AND ASSIMP_IMPORTER_GLTF_USE_OPEN3DGC)
-  TARGET_LINK_LIBRARIES(assimp rt)
+  TARGET_LINK_LIBRARIES(assimp PRIVATE rt)
 ENDIF ()
 
 IF(ASSIMP_INSTALL)
diff --git a/code/Common/BaseImporter.cpp b/code/Common/BaseImporter.cpp
index 5c70cc2..bbbaae0 100644
--- a/code/Common/BaseImporter.cpp
+++ b/code/Common/BaseImporter.cpp
@@ -354,7 +354,7 @@ std::string BaseImporter::GetExtension(const std::string &pFile) {
     return false;
 }
 
-#include "utf8.h"
+#include <utf8cpp/utf8.h>
 
 // ------------------------------------------------------------------------------------------------
 // Convert to UTF8 data
diff --git a/code/Common/StbCommon.h b/code/Common/StbCommon.h
index aef23ce..1b059ec 100644
--- a/code/Common/StbCommon.h
+++ b/code/Common/StbCommon.h
@@ -53,7 +53,7 @@ OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 #pragma clang diagnostic ignored "-Wunused-function"
 #endif
 
-#ifndef STB_USE_HUNTER
+#if 0
 /*  Use prefixed names for the symbols from stb_image as it is a very commonly embedded library.
     Including vanilla stb_image symbols causes duplicate symbol problems if assimp is linked
     statically together with another library or executable that also embeds stb_image.
@@ -113,7 +113,7 @@ OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 #define stbi_zlib_decode_noheader_malloc assimp_stbi_zlib_decode_noheader_malloc
 #endif
 
-#include "stb/stb_image.h"
+#include <stb_image.h>
 
 #if _MSC_VER
 #pragma warning(pop)
diff --git a/code/Common/ZipArchiveIOSystem.cpp b/code/Common/ZipArchiveIOSystem.cpp
index 23d7db1..8f9b9ef 100644
--- a/code/Common/ZipArchiveIOSystem.cpp
+++ b/code/Common/ZipArchiveIOSystem.cpp
@@ -54,7 +54,7 @@ OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 #ifdef ASSIMP_USE_HUNTER
 #    include <minizip/unzip.h>
 #else
-#    include <unzip.h>
+#    include <minizip/unzip.h>
 #endif
 
 namespace Assimp {
