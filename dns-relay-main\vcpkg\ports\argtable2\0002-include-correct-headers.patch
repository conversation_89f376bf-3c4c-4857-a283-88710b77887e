From 271423adb2900c8bf4aad5d73d7ef1efbb5c35d8 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 22 Sep 2020 15:17:18 -0700
Subject: [PATCH 2/2] include correct headers

---
 src/arg_date.c | 4 +++-
 src/arg_int.c  | 1 +
 src/getopt.c   | 1 +
 3 files changed, 5 insertions(+), 1 deletion(-)

diff --git a/src/arg_date.c b/src/arg_date.c
index 22b59e2..5004b42 100644
--- a/src/arg_date.c
+++ b/src/arg_date.c
@@ -37,6 +37,8 @@ USA.
 #include <strings.h>
 #endif
 
+#include <string.h>
+
 #include "argtable2.h"
 
 /* local error codes  */
@@ -112,7 +114,7 @@ static void errorfn(struct arg_date *parent, FILE *fp, int errorcode, const char
             char buff[200];           
 
             fprintf(fp,"illegal timestamp format \"%s\"\n",argval);
-            bzero(&tm,sizeof(tm));
+            memset(&tm, 0, sizeof(tm));
             strptime("1999-12-31 23:59:59","%F %H:%M:%S",&tm);
             strftime(buff, sizeof(buff), parent->format, &tm);
             printf("correct format is \"%s\"\n", buff);                        
diff --git a/src/arg_int.c b/src/arg_int.c
index 29c20e5..bc10012 100644
--- a/src/arg_int.c
+++ b/src/arg_int.c
@@ -30,6 +30,7 @@ USA.
 
 #include "argtable2.h"
 #include <limits.h>
+#include <ctype.h>
 
 /* local error codes */
 enum {EMINCOUNT=1,EMAXCOUNT,EBADINT,EOVERFLOW};
diff --git a/src/getopt.c b/src/getopt.c
index b208529..b13acc6 100644
--- a/src/getopt.c
+++ b/src/getopt.c
@@ -47,6 +47,7 @@
 #endif
 
 #include <stdio.h>
+#include <string.h>
 
 /* Comment out all this code if we are using the GNU C Library, and are not
    actually compiling the library itself.  This code is part of the GNU C
-- 
2.24.3 (Apple Git-128)

