diff --git a/CMakeLists.txt b/CMakeLists.txt
index e5a01e7..ce1fa4f 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -47,7 +47,7 @@ endif()
 set( CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${COMPILE_FLAGS}" )
 
 # Adding cppcheck properties
-if( CMAKE_BUILD_TYPE STREQUAL "Debug" )
+if(0)
     set( cppcheck cppcheck "--enable=warning" "--inconclusive" "--force" "--inline-suppr" )
     set_target_properties( arsenalgear PROPERTIES CXX_CPPCHECK ${cppcheck})
 endif()
