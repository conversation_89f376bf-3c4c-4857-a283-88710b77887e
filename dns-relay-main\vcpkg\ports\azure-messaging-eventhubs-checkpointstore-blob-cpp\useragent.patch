diff --git a/sdk/eventhubs/azure-messaging-eventhubs-checkpointstore-blob/CMakeLists.txt b/sdk/eventhubs/azure-messaging-eventhubs-checkpointstore-blob/CMakeLists.txt
index f776b3a94..c9bf65363 100644
--- a/sdk/eventhubs/azure-messaging-eventhubs-checkpointstore-blob/CMakeLists.txt
+++ b/sdk/eventhubs/azure-messaging-eventhubs-checkpointstore-blob/CMakeLists.txt
@@ -69,6 +69,7 @@ add_library(
   azure-messaging-eventhubs-checkpointstore-blob
     ${AZURE_MESSAGING_EVENTHUBS_BLOB_CHECKPOINT_HEADER} ${AZURE_MESSAGING_EVENTHUBS_BLOB_CHECKPOINT_SOURCE}
 )
+target_compile_definitions(azure-messaging-eventhubs-checkpointstore-blob PRIVATE _azure_BUILDING_SDK)
 create_per_service_target_build(eventhubs azure-messaging-eventhubs-checkpointstore-blob)
 add_library(Azure::azure-messaging-eventhubs-checkpointstore-blob ALIAS azure-messaging-eventhubs-checkpointstore-blob)
 
